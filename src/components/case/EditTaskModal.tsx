import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { EnhancedDateTimePicker } from "@/components/ui/enhanced-datetime-picker";
import { useToast } from "@/hooks/use-toast";
import { useCaseTasks, CaseTask } from "@/hooks/useCaseTasks";
import { useUserManagement } from "@/hooks/useUserManagement";
import { Loader2 } from "lucide-react";

interface EditTaskModalProps {
  isOpen: boolean;
  onClose: () => void;
  task: CaseTask | null;
  caseId?: string;
}

export const EditTaskModal = ({ isOpen, onClose, task, caseId }: EditTaskModalProps) => {
  const { toast } = useToast();
  const { updateTask } = useCaseTasks(caseId);
  const { users } = useUserManagement();
  const [isLoading, setIsLoading] = useState(false);
  
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    priority: "בינוני",
    deadline: "",
    assigned_to: "",
    status: "לא הושלם",
    reminders_enabled: true,
  });

  // Reset form when task changes
  useEffect(() => {
    if (task && isOpen) {
      setFormData({
        title: task.title || "",
        description: task.description || "",
        priority: task.priority || "בינוני",
        deadline: task.deadline || "",
        assigned_to: task.assigned_to || "unassigned",
        status: task.status || "לא הושלם",
        reminders_enabled: task.reminders_enabled ?? true,
      });
    }
  }, [task, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      toast({
        title: "שגיאה",
        description: "נא להזין כותרת למשימה",
        variant: "destructive",
      });
      return;
    }

    if (!task) {
      toast({
        title: "שגיאה",
        description: "לא נמצאה משימה לעדכון",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const updateData: any = {
        title: formData.title.trim(),
        priority: formData.priority,
        status: formData.status,
        reminders_enabled: formData.reminders_enabled,
      };

      // Only include optional fields if they have values
      if (formData.description && formData.description.trim()) {
        updateData.description = formData.description.trim();
      }

      if (formData.deadline) {
        updateData.deadline = formData.deadline;
      }

      if (formData.assigned_to && formData.assigned_to !== "unassigned") {
        updateData.assigned_to = formData.assigned_to;
      }

      // Handle completed_at field based on status change
      if (formData.status === 'הושלם' && task.status !== 'הושלם') {
        updateData.completed_at = new Date().toISOString();
      } else if (formData.status !== 'הושלם' && task.status === 'הושלם') {
        updateData.completed_at = null;
      }

      await updateTask(task.id, updateData);
      
      onClose();
      
      toast({
        title: "הצלחה",
        description: "המשימה עודכנה בהצלחה",
      });
    } catch (error) {
      console.error('Error updating task:', error);
      toast({
        title: "שגיאה",
        description: "שגיאה בעדכון המשימה",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  const priorityOptions = [
    "נמוך",
    "בינוני", 
    "גבוה",
    "קריטי"
  ];

  const statusOptions = [
    "לא הושלם",
    "בתהליך",
    "הושלם"
  ];

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">עריכת משימה</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="title">כותרת המשימה *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              placeholder="הזן כותרת למשימה..."
              disabled={isLoading}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">תיאור המשימה</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              placeholder="הזן תיאור למשימה..."
              rows={3}
              disabled={isLoading}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="priority">עדיפות</Label>
              <Select 
                value={formData.priority} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, priority: value }))}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {priorityOptions.map((priority) => (
                    <SelectItem key={priority} value={priority}>
                      {priority}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">סטטוס</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value }))}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {statusOptions.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="assigned_to">משתמש מוקצה</Label>
            <Select 
              value={formData.assigned_to} 
              onValueChange={(value) => setFormData(prev => ({ ...prev, assigned_to: value }))}
              disabled={isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="בחר משתמש..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="unassigned">ללא הקצאה</SelectItem>
                {users.map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name} ({user.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <EnhancedDateTimePicker
            label="תאריך יעד"
            value={formData.deadline}
            onChange={(value) => setFormData(prev => ({ ...prev, deadline: value }))}
            placeholder="בחר תאריך ושעה..."
            disabled={isLoading}
          />

          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border" dir="ltr">
            <Switch
              id="reminders"
              checked={formData.reminders_enabled}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, reminders_enabled: checked }))}
              disabled={isLoading}
            />
            <div className="space-y-1 text-right">
              <Label htmlFor="reminders" className="text-sm font-medium">
                תזכורות WhatsApp
              </Label>
              <p className="text-xs text-gray-600">
                קבל תזכורות 7, 3 ויום אחד לפני המועד הסופי
              </p>
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-6 border-t">
            <Button 
              type="button" 
              variant="outline" 
              onClick={handleClose}
              disabled={isLoading}
            >
              ביטול
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin ml-2" />
                  שומר...
                </>
              ) : (
                "שמור שינויים"
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
